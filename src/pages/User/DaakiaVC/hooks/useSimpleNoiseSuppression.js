import { useEffect, useRef, useState } from 'react';
import { Track } from 'livekit-client';
import { useIsMuted } from '@livekit/components-react';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';

/**
 * Simple, clean noise suppression hook following LiveKit best practices
 * 
 * Key principles:
 * 1. Use LiveKit's built-in hooks (useIsMuted)
 * 2. Minimal state management
 * 3. Clear error handling
 * 4. Automatic cleanup
 * 5. Device change handling
 */
export const useSimpleNoiseSuppression = (room) => {
  const { noiseSuppressionEnabled, setNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  
  // Use LiveKit's built-in hook for microphone state - works across all devices
  const isMicrophoneMuted = useIsMuted(Track.Source.Microphone);
  
  // Simple state
  const [isProcessing, setIsProcessing] = useState(false);
  const [isActive, setIsActive] = useState(false);
  
  // Refs for cleanup
  const processorRef = useRef(null);
  const isApplyingRef = useRef(false);
  
  // Track the current device to detect changes
  const currentDeviceRef = useRef(null);

  // Main effect: Apply or remove noise suppression
  useEffect(() => {
    const applyNoiseSuppression = async () => {
      // Basic checks
      if (!room || room.state !== "connected") return;
      if (isApplyingRef.current) return; // Prevent concurrent operations
      if (isMicrophoneMuted) return; // Don't process muted microphone
      
      // Check if we need to do anything
      if (noiseSuppressionEnabled === isActive) return;

      isApplyingRef.current = true;
      setIsProcessing(true);

      try {
        const audioPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
        if (!audioPublication?.track) {
          console.log('No audio track available');
          return;
        }

        const audioTrack = audioPublication.track;
        const mediaStreamTrack = audioTrack.mediaStreamTrack;

        // Validate track is ready
        if (mediaStreamTrack.readyState !== 'live') {
          console.log('Audio track not ready');
          return;
        }

        if (noiseSuppressionEnabled && !isActive) {
          // Enable noise suppression
          console.log('Enabling noise suppression...');
          
          const processor = new NoiseSuppressionProcessor();
          const processedTrack = await processor.startProcessing(mediaStreamTrack);
          
          if (processedTrack) {
            await audioTrack.replaceTrack(processedTrack, true);
            processorRef.current = processor;
            setIsActive(true);
            console.log('Noise suppression enabled');
          }
          
        } else if (!noiseSuppressionEnabled && isActive) {
          // Disable noise suppression
          console.log('Disabling noise suppression...');
          
          if (processorRef.current) {
            await processorRef.current.stopProcessing();
            processorRef.current = null;
          }
          
          // Get fresh audio track
          const settings = mediaStreamTrack.getSettings();
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              deviceId: settings.deviceId || 'default',
              echoCancellation: true,
              noiseSuppression: false,
              autoGainControl: true
            }
          });
          
          const newTrack = stream.getAudioTracks()[0];
          if (newTrack) {
            await audioTrack.replaceTrack(newTrack, true);
            setIsActive(false);
            console.log('Noise suppression disabled');
          }
        }

        // Update device tracking
        const settings = mediaStreamTrack.getSettings();
        currentDeviceRef.current = settings.deviceId;

      } catch (error) {
        console.error('Noise suppression error:', error);
        // Reset state on error
        setIsActive(false);
        if (processorRef.current) {
          try {
            await processorRef.current.stopProcessing();
          } catch (cleanupError) {
            console.error('Cleanup error:', cleanupError);
          }
          processorRef.current = null;
        }
      } finally {
        setIsProcessing(false);
        isApplyingRef.current = false;
      }
    };

    // Run with small delay to ensure track is ready
    const timeoutId = setTimeout(applyNoiseSuppression, 100);
    return () => clearTimeout(timeoutId);
    
  }, [room, room?.state, noiseSuppressionEnabled, isActive, isMicrophoneMuted]);

  // Device change detection effect
  useEffect(() => {
    if (!room || room.state !== "connected") return;

    const checkDeviceChange = () => {
      const audioPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      if (!audioPublication?.track) return;

      const settings = audioPublication.track.mediaStreamTrack.getSettings();
      const newDeviceId = settings.deviceId;

      if (currentDeviceRef.current && currentDeviceRef.current !== newDeviceId) {
        console.log('Audio device changed, disabling noise suppression');
        
        // Clean up processor
        if (processorRef.current) {
          processorRef.current.stopProcessing().catch(console.error);
          processorRef.current = null;
        }
        
        // Reset states
        setIsActive(false);
        setNoiseSuppressionEnabled(false);
        currentDeviceRef.current = newDeviceId;
      }
    };

    // Check every 2 seconds
    const interval = setInterval(checkDeviceChange, 2000);
    return () => clearInterval(interval);
    
  }, [room, room?.state, setNoiseSuppressionEnabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (processorRef.current) {
        processorRef.current.stopProcessing().catch(console.error);
        processorRef.current = null;
      }
      isApplyingRef.current = false;
    };
  }, []);

  return {
    isNoiseSuppressionActive: isActive,
    isProcessing,
    isMicrophoneEnabled: !isMicrophoneMuted, // Simple inversion
  };
};
