import { useEffect, useRef, useState } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';

export const useNoiseSuppression = (room) => {
  const { noiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessorRef = useRef(null);
  const originalTrackRef = useRef(null);
  const processedTrackRef = useRef(null);

  const [isProcessing, setIsProcessing] = useState(false);
  const [isNoiseSuppressionActive, setIsNoiseSuppressionActive] = useState(false);

  // Add ref to track current operation and allow cancellation
  const currentOperationRef = useRef(null);

  useEffect(() => {
    const applyNoiseSuppression = async () => {
      if (!room || room.state !== "connected") return;
      if (isProcessing) {
        return;
      }
      if (noiseSuppressionEnabled === isNoiseSuppressionActive) {
        return;
      }

      // Cancel any existing operation
      if (currentOperationRef.current) {
        currentOperationRef.current.cancelled = true;
      }

      // Create new operation tracker
      const currentOperation = { cancelled: false };
      currentOperationRef.current = currentOperation;

      try {
        setIsProcessing(true);

        // Check if operation was cancelled before proceeding
        if (currentOperation.cancelled) {
          return;
        }
        const audioTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
        if (!audioTrackPublication || !audioTrackPublication.track) {
          return;
        }

        const localAudioTrack = audioTrackPublication.track;

        if (noiseSuppressionEnabled && !isNoiseSuppressionActive) {
          // Check cancellation before starting expensive operations
          if (currentOperation.cancelled) {
            return;
          }

          // Store the original track before processing (clone it to preserve)
          if (!originalTrackRef.current) {
            originalTrackRef.current = localAudioTrack.mediaStreamTrack.clone();
          }
          noiseProcessorRef.current = new NoiseSuppressionProcessor();

          // Check cancellation before async processing
          if (currentOperation.cancelled) {
            // Clean up cloned track if operation was cancelled
            if (originalTrackRef.current) {
              originalTrackRef.current.stop();
              originalTrackRef.current = null;
            }
            return;
          }

          const processedTrack = await noiseProcessorRef.current.startProcessing(
            localAudioTrack.mediaStreamTrack
          );

          // Check cancellation after async processing
          if (currentOperation.cancelled) {
            // Clean up processed track if operation was cancelled
            if (processedTrack) {
              processedTrack.stop();
            }
            // Clean up cloned track
            if (originalTrackRef.current) {
              originalTrackRef.current.stop();
              originalTrackRef.current = null;
            }
            return;
          }

          if (processedTrack) {
            // Store the processed track reference
            processedTrackRef.current = processedTrack;

            // Replace the original track with the processed one
            await localAudioTrack.replaceTrack(processedTrack, true);

            // Final cancellation check before setting state
            if (currentOperation.cancelled) {
              return;
            }

            // Mark noise suppression as active
            setIsNoiseSuppressionActive(true);
          }
        } else if (!noiseSuppressionEnabled && isNoiseSuppressionActive) {
          // Check cancellation before starting disable operations
          if (currentOperation.cancelled) {
            return;
          }

          try {
            // Stop the noise processor first
            if (noiseProcessorRef.current) {
              await noiseProcessorRef.current.stopProcessing();
              noiseProcessorRef.current = null;
            }
            processedTrackRef.current = null;

            // Check cancellation after stopping processor
            if (currentOperation.cancelled) {
              return;
            }

            // Create a fresh audio track to replace the processed one
            const currentSettings = localAudioTrack.mediaStreamTrack.getSettings();
            const constraints = {
              audio: {
                deviceId: currentSettings.deviceId || 'default',
                echoCancellation: true,
                noiseSuppression: false, // Disable browser noise suppression
                autoGainControl: true
              }
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);

            // Check cancellation after getUserMedia
            if (currentOperation.cancelled) {
              // Clean up the new stream if operation was cancelled
              stream.getTracks().forEach(track => track.stop());
              return;
            }

            const newAudioTrack = stream.getAudioTracks()[0];

            if (newAudioTrack) {
              await localAudioTrack.replaceTrack(newAudioTrack, true);
            }

            // Final cancellation check before cleanup and state update
            if (currentOperation.cancelled) {
              // Clean up the new track if operation was cancelled after replacement
              if (newAudioTrack) {
                newAudioTrack.stop();
              }
              return;
            }

            // Clear references
            if (originalTrackRef.current) {
              originalTrackRef.current.stop();
              originalTrackRef.current = null;
            }

            // Mark noise suppression as inactive
            setIsNoiseSuppressionActive(false);
          } catch (restoreError) {
            console.error('Failed to restore original audio track:', restoreError);
            // Still set state to inactive even if restore failed
            setIsNoiseSuppressionActive(false);
          }
        }
      } catch (error) {
        console.error('Noise suppression operation failed:', error);
        // Reset processing state on any error
        setIsNoiseSuppressionActive(false);

        // Clean up any tracks that might have been created but not properly handled
        if (originalTrackRef.current) {
          originalTrackRef.current.stop();
          originalTrackRef.current = null;
        }
        if (processedTrackRef.current) {
          processedTrackRef.current.stop();
          processedTrackRef.current = null;
        }
      } finally {
        setIsProcessing(false);
        // Clear the current operation reference
        if (currentOperationRef.current === currentOperation) {
          currentOperationRef.current = null;
        }
      }
    };

    // Only run when room is connected and we need to change state
    if (room && room.state === "connected") {
      // Small delay to ensure audio track is fully initialized
      setTimeout(applyNoiseSuppression, 500);
    }

    // No cleanup function - let the state persist
  }, [room, room?.state, noiseSuppressionEnabled, isProcessing, isNoiseSuppressionActive]);

  // Separate cleanup effect for when component unmounts
  useEffect(() => {
    return () => {
      // Cancel any ongoing operations
      if (currentOperationRef.current) {
        currentOperationRef.current.cancelled = true;
        currentOperationRef.current = null;
      }

      if (noiseProcessorRef.current) {
        try {
          noiseProcessorRef.current.stopProcessing();
          noiseProcessorRef.current = null;
        } catch (error) {
          console.error('Failed to stop noise processor during cleanup:', error);
        }
      }

      // Clean up all track references
      if (originalTrackRef.current) {
        originalTrackRef.current.stop();
        originalTrackRef.current = null;
      }

      if (processedTrackRef.current) {
        // Stop processed track if it's still active
        try {
          processedTrackRef.current.stop();
        } catch (error) {
          // Track might already be stopped, ignore error
        }
        processedTrackRef.current = null;
      }

      setIsProcessing(false);
      setIsNoiseSuppressionActive(false);
    };
  }, []);

  // Return hook state and methods
  return {
    isNoiseSuppressionActive,
    isProcessing,
  };
};
 